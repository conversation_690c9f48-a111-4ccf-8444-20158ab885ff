# 账户余额接口接入说明

## 接口信息
- **接口地址**: `POST /app/account/balance`
- **接口描述**: 获取用户账户余额信息
- **需要登录**: 是

## 已完成的工作

### 1. API接口定义
在 `api/index.js` 文件中添加了账户余额接口：

```javascript
// 获取账户余额
getAccountBalance() {
    return post('/app/account/balance');
},
```

### 2. 页面集成
在 `subpages/finance/withdraw.vue` 页面中：

- 导入了 `userApi` 
- 在 `onLoad` 生命周期中调用接口
- 添加了下拉刷新功能
- 添加了手动刷新按钮
- 添加了加载状态管理
- 添加了错误处理

### 3. 数据字段映射
根据实际接口返回数据，字段映射如下：

```javascript
// 可提现金额 (balance)
this.availableAmount = res.data.balance || 0;

// 待入账金额 (drz)
this.pendingAmount = res.data.drz || 0;

// 提现中金额 (tiXianIng)
this.withdrawingAmount = res.data.tiXianIng || 0;

// 已提现金额 (yiTiXian)
this.totalWithdrawn = res.data.yiTiXian || 0;
```

## 测试方法

### 1. 页面加载测试
- 打开提现页面，会自动调用接口
- 查看控制台日志，确认接口调用情况

### 2. 手动刷新测试
- 点击"刷新余额"按钮
- 观察按钮状态变化和数据更新

### 3. 下拉刷新测试
- 在页面顶部下拉
- 观察刷新动画和数据更新

## 接口返回数据格式

实际接口返回的数据格式：

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "balance": 0.00,        // 可提现余额
    "tiXianIng": 0.00,      // 提现中金额
    "yiTiXian": 0.00,       // 已提现金额
    "drz": 0,               // 待入账金额
    "tixianConfig": {       // 提现配置（暂时不使用）
      "minAmt": 10,         // 最小提现金额
      "fee": 3,             // 手续费
      "rate": 8,            // 费率
      "flag": true          // 是否启用
    }
  },
  "ok": true
}
```

## 注意事项

1. **字段映射**: 已根据实际接口返回数据完成字段映射
2. **错误处理**: 接口调用失败时会显示提示信息，并保持使用默认数据
3. **加载状态**: 添加了防重复请求机制
4. **日志输出**: 在开发阶段会输出详细的调试信息

## ✅ 接入完成

接口已成功接入，字段映射已完成：
- ✅ 可提现余额：`balance` → `availableAmount`
- ✅ 待入账金额：`drz` → `pendingAmount`
- ✅ 提现中金额：`tiXianIng` → `withdrawingAmount`
- ✅ 已提现金额：`yiTiXian` → `totalWithdrawn`

现在可以正常使用提现页面，数据会从接口实时获取并显示。
