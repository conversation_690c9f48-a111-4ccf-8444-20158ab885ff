# 账户余额接口接入说明

## 接口信息
- **接口地址**: `POST /app/account/balance`
- **接口描述**: 获取用户账户余额信息
- **需要登录**: 是

## 已完成的工作

### 1. API接口定义
在 `api/index.js` 文件中添加了账户余额接口：

```javascript
// 获取账户余额
getAccountBalance() {
    return post('/app/account/balance');
},
```

### 2. 页面集成
在 `subpages/finance/withdraw.vue` 页面中：

- 导入了 `userApi` 
- 在 `onLoad` 生命周期中调用接口
- 添加了下拉刷新功能
- 添加了手动刷新按钮
- 添加了加载状态管理
- 添加了错误处理

### 3. 数据字段映射
当前代码支持以下字段名（会自动适配）：

```javascript
// 可提现金额
this.availableAmount = res.data.availableAmount || res.data.available || 0;

// 待入账金额  
this.pendingAmount = res.data.pendingAmount || res.data.pending || 0;

// 提现中金额
this.withdrawingAmount = res.data.withdrawingAmount || res.data.withdrawing || 0;

// 已提现金额
this.totalWithdrawn = res.data.totalWithdrawn || res.data.totalWithdraw || 0;
```

## 测试方法

### 1. 页面加载测试
- 打开提现页面，会自动调用接口
- 查看控制台日志，确认接口调用情况

### 2. 手动刷新测试
- 点击"刷新余额"按钮
- 观察按钮状态变化和数据更新

### 3. 下拉刷新测试
- 在页面顶部下拉
- 观察刷新动画和数据更新

## 接口返回数据格式

请根据实际接口返回的数据格式，调整字段映射。预期的数据格式：

```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "availableAmount": 127.99,    // 可提现金额
    "pendingAmount": 327.12,      // 待入账金额
    "withdrawingAmount": 327.12,  // 提现中金额
    "totalWithdrawn": 1327.12     // 已提现金额
  }
}
```

## 注意事项

1. **字段名适配**: 如果接口返回的字段名与当前代码不匹配，请修改 `loadWithdrawData` 方法中的字段映射
2. **错误处理**: 接口调用失败时会显示提示信息，并保持使用默认数据
3. **加载状态**: 添加了防重复请求机制
4. **日志输出**: 在开发阶段会输出详细的调试信息

## 下一步工作

等您提供接口返回的具体数据结构后，我将：
1. 调整字段映射
2. 优化数据渲染逻辑
3. 完善错误处理
4. 添加数据格式化（如金额显示格式）
