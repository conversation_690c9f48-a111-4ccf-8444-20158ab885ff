{
	"pages": [ //主包页面 - 核心功能页面
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "首页",
				"navigationBarBackgroundColor": "#ffffff",
				"navigationBarTextStyle": "black",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/login/login",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/earnings/earnings",
			"style": {
				"navigationBarTitleText": "我的收益",
				"navigationBarBackgroundColor": "#f5f5f5",
				"navigationBarTextStyle": "black"
			}
		},
		{
			"path": "pages/news_list/index",
			"style": {
				"navigationBarTitleText": "商学院",
				"navigationBarBackgroundColor": "#ffffff",
				"navigationBarTextStyle": "black"
			}
		},
		{
			"path": "pages/my/my",
			"style": {
				"navigationStyle": "custom"
			}
		}
	],
	"subPackages": [
		{
			"root": "subpages/auth",
			"name": "auth",
			"pages": [
				{
					"path": "forgot-password",
					"style": {
						"navigationBarTitleText": "忘记密码",
						"navigationBarBackgroundColor": "#F6F8FF",
						"navigationBarTextStyle": "black"
					}
				},
				{
					"path": "reset-password",
					"style": {
						"navigationBarTitleText": "修改登录密码",
						"navigationBarBackgroundColor": "#F6F8FF",
						"navigationBarTextStyle": "black"
					}
				},
				{
					"path": "realname",
					"style": {
						"navigationBarTitleText": "实名认证",
						"navigationBarBackgroundColor": "#f5f5f5",
						"navigationBarTextStyle": "black"
					}
				}
			]
		},
		{
			"root": "subpages/user",
			"name": "user",
			"pages": [
				{
					"path": "profile",
					"style": {
						"navigationStyle": "custom"
					}
				},
				{
					"path": "settings",
					"style": {
						"navigationBarTitleText": "设置",
						"navigationBarBackgroundColor": "#f5f5f5",
						"navigationBarTextStyle": "black"
					}
				},
				{
					"path": "contact",
					"style": {
						"navigationStyle": "custom"
					}
				},
				{
					"path": "bank_info/index",
					"style": {
						"navigationBarTitleText": "银行卡",
						"navigationBarBackgroundColor": "#f5f5f5",
						"navigationBarTextStyle": "black"
					}
				}
			]
		},
		{
			"root": "subpages/finance",
			"name": "finance",
			"pages": [
				{
					"path": "withdraw",
					"style": {
						"navigationBarTitleText": "提现",
						"navigationBarBackgroundColor": "#f5f5f5",
						"navigationBarTextStyle": "black",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "withdraw-record",
					"style": {
						"navigationBarTitleText": "提现记录",
						"navigationBarBackgroundColor": "#f5f5f5",
						"navigationBarTextStyle": "black",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "withdraw-detail",
					"style": {
						"navigationBarTitleText": "提现明细",
						"navigationBarBackgroundColor": "#f5f5f5",
						"navigationBarTextStyle": "black"
					}
				},
				{
					"path": "add-card",
					"style": {
						"navigationBarTitleText": "添加银行卡",
						"navigationBarBackgroundColor": "#f5f5f5",
						"navigationBarTextStyle": "black"
					}
				},
				{
					"path": "withdraw-success",
					"style": {
						"navigationBarTitleText": "提现成功",
						"navigationBarBackgroundColor": "#f5f5f5",
						"navigationBarTextStyle": "black"
					}
				},
				{
					"path": "withdraw-fail",
					"style": {
						"navigationBarTitleText": "提现失败",
						"navigationBarBackgroundColor": "#f5f5f5",
						"navigationBarTextStyle": "black"
					}
				}
			]
		},
		{
			"root": "subpages/order",
			"name": "order",
			"pages": [
				{
					"path": "list",
					"style": {
						"navigationBarTitleText": "订单列表",
						"navigationBarBackgroundColor": "#f5f5f5",
						"navigationBarTextStyle": "black"
					}
				},
				{
					"path": "detail",
					"style": {
						"navigationBarTitleText": "订单详情",
						"navigationBarBackgroundColor": "#f5f5f5",
						"navigationBarTextStyle": "black"
					}
				},
				{
					"path": "confirm",
					"style": {
						"navigationBarTitleText": "订单结算",
						"navigationBarBackgroundColor": "#f5f5f5",
						"navigationBarTextStyle": "black"
					}
				}
			]
		},
		{
			"root": "subpages/address",
			"name": "address",
			"pages": [
				{
					"path": "list",
					"style": {
						"navigationBarTitleText": "地址管理",
						"navigationBarBackgroundColor": "#f5f5f5",
						"navigationBarTextStyle": "black"
					}
				},
				{
					"path": "edit",
					"style": {
						"navigationBarTitleText": "编辑地址",
						"navigationBarBackgroundColor": "#f5f5f5",
						"navigationBarTextStyle": "black"
					}
				}
			]
		},
		{
			"root": "subpages/news",
			"name": "news",
			"pages": [
				{
					"path": "detail",
					"style": {
						"navigationBarTitleText": "资讯详情"
					}
				}
			]
		},
		{
			"root": "subpages/product",
			"name": "product",
			"pages": [
				{
					"path": "detail",
					"style": {
						"navigationStyle": "custom"
					}
				}
			]
		},
		{
			"root": "subpages/payment",
			"name": "payment",
			"pages": [
				{
					"path": "pay",
					"style": {
						"navigationBarTitleText": "订单支付",
						"navigationBarBackgroundColor": "#f5f5f5",
						"navigationBarTextStyle": "black"
					}
				},
				{
					"path": "result",
					"style": {
						"navigationBarTitleText": "支付结果",
						"navigationBarBackgroundColor": "#f5f5f5",
						"navigationBarTextStyle": "black"
					}
				}
			]
		},
		{
			"root": "subpages/agent",
			"name": "agent",
			"pages": [
				{
					"path": "agent",
					"style": {
						"navigationBarTitleText": "代理商列表",
						"navigationBarBackgroundColor": "#f5f5f5",
						"navigationBarTextStyle": "black"
					}
				},
				{
					"path": "addAgent",
					"style": {
						"navigationBarTitleText": "新增代理商",
						"navigationBarBackgroundColor": "#f5f5f5",
						"navigationBarTextStyle": "black"
					}
				}
			]
		},
		{
			"root": "subpages/team",
			"name": "team",
			"pages": [
				{
					"path": "team",
					"style": {
						"navigationBarTitleText": "团队列表",
						"navigationBarBackgroundColor": "#f5f5f5",
						"navigationBarTextStyle": "black"
					}
				},
				{
					"path": "addTeam",
					"style": {
						"navigationBarTitleText": "新增团队",
						"navigationBarBackgroundColor": "#f5f5f5",
						"navigationBarTextStyle": "black"
					}
				}
			]
		},
		{
			"root": "subpages/merchant",
			"name": "merchant",
			"pages": [
				{
					"path": "merchant",
					"style": {
						"navigationBarTitleText": "商户列表",
						"navigationBarBackgroundColor": "#f5f5f5",
						"navigationBarTextStyle": "black"
					}
				},
				{
					"path": "addMerchant",
					"style": {
						"navigationBarTitleText": "新增商户",
						"navigationBarBackgroundColor": "#f5f5f5",
						"navigationBarTextStyle": "black"
					}
				}
			]
		},
		{
			"root": "subpages/notice",
			"name": "notice",
			"pages": [
				{
					"path": "noticesList",
					"style": {
						"navigationBarTitleText": "公告",
						"navigationBarBackgroundColor": "#f5f5f5",
						"navigationBarTextStyle": "black"
					}
				}
			]
		}
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "分销小程序",
		"navigationBarBackgroundColor": "#ffffff",
		"backgroundColor": "#f8f8f8"
	},
	"tabBar": {
		"color": "#999999",
		"selectedColor": "#5856D6",
		"backgroundColor": "#ffffff",
		"borderStyle": "black",
		"list": [
			{
				"pagePath": "pages/index/index",
				"text": "首页",
				"iconPath": "static/images/icon_home_tab.png",
				"selectedIconPath": "static/images/icon_home_click_tab.png"
			},
			{
				"pagePath": "pages/earnings/earnings",
				"text": "收益",
				"iconPath": "static/images/icon_earnings_tab.png",
				"selectedIconPath": "static/images/icon_earnings_click_tab.png"
			},
			{
				"pagePath": "pages/news_list/index",
				"text": "商学院",
				"iconPath": "static/images/icon_college_tab.png",
				"selectedIconPath": "static/images/icon_ college_click_tab.png"
			},
			{
				"pagePath": "pages/my/my",
				"text": "我的",
				"iconPath": "static/images/icon_my_tab.png",
				"selectedIconPath": "static/images/icon_my_click_tab.png"
			}
		]
	},
	"uniIdRouter": {}
}
