<template>
	<view class="page">
		<!-- 提现数据模块 -->
		<view class="withdraw-data-section">
			<view class="data-content">
				<!-- 主要金额显示 -->
				<view class="main-amount-section">
					<view class="data-label" style="padding-left: 10rpx;">可提现(元)</view>
					<view class="main-amount">{{ availableAmount }}</view>
					<view class="record-link" @click="goToRecord">
						<text class="record-text">查看提现记录</text>
						<uni-icons type="right" size="16" color="#ffffff"></uni-icons>
					</view>
				</view>

				<!-- 底部三个数据 -->
				<view class="bottom-data-section">
					<view class="data-item">
						<view class="data-label">待入账(元)</view>
						<view class="data-amount">{{ pendingAmount }}</view>
					</view>
					<view class="data-item">
						<view class="data-label">提现中(元)</view>
						<view class="data-amount">{{ withdrawingAmount }}</view>
					</view>
					<view class="data-item">
						<view class="data-label">已提现(元)</view>
						<view class="data-amount">{{ totalWithdrawn }}</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 提现方式模块 -->
		<view class="section-title">
			<view class="title-line"></view>
			<text class="title-text">提现方式</text>
		</view>
		<view class="withdraw-method-section">
			<view class="method-list">
				<view class="method-item" @click="selectMethod()">
					<view class="method-left">
						<text class="method-label">到账方式</text>
					</view>
					<view class="method-right">
						<image class="wechat-icon" :src="currentMethodInfo.icon" mode="aspectFill"></image>
						<text class="method-name">{{ currentMethodInfo.name }}</text>
						<uni-icons type="right" size="16" color="#999999"></uni-icons>
					</view>
				</view>
			</view>
		</view>

		<!-- 提现金额模块 -->
		<view class="withdraw-amount-section">
			<view class="amount-label-section">
				<text class="amount-label">提现金额(元)</text>
			</view>

			<view class="amount-input-container">
				<view class="input-wrapper">
					<text class="currency-symbol">¥</text>
					<input class="amount-input" type="digit" placeholder="请输入提现金额" v-model="withdrawAmount"
						@input="onAmountInput" />
				</view>
			</view>

			<view class="available-amount-tip">
				<text class="tip-text">可提现金额¥{{ availableAmount }}</text>
			</view>
		</view>
		<view class="notice-text">{{ withdrawNoticeText }}</view>

		<!-- 提现按钮 -->
		<view class="withdraw-btn-section">
			<button class="withdraw-btn"  @click="handleWithdraw">
				立即提现
			</button>
		</view>

		<!-- 提现方式选择弹窗 -->
		<view class="modal-overlay" v-if="showMethodModal" @click="closeMethodModal">
			<view class="method-modal" @click.stop>
				<!-- 弹窗标题 -->
				<view class="modal-header">
					<text class="modal-title">提现</text>
					<view class="close-btn" @click="closeMethodModal">
						<text class="close-icon">×</text>
					</view>
				</view>

				<!-- 弹窗内容 -->
				<view class="modal-content">
					<!-- 提现到微信 -->
					<view class="method-section">
						<view class="section-header">
							<view class="section-line"></view>
							<text class="section-title">提现到微信</text>
						</view>
						<view class="method-option" :class="{ selected: selectedMethod === 'wechat' }" @click="selectWithdrawMethod('wechat')">
							<view class="option-content">
								<image class="wechat-icon-modal" src="/static/images/icon_weixin_pay.png" mode="aspectFill"></image>
								<text class="option-text">提现到微信</text>
							</view>
							<view class="check-icon" v-if="selectedMethod === 'wechat'">
								<text class="check-mark">✓</text>
							</view>
						</view>

						<!-- 微信提现详细信息 - 一直显示 -->
						<view class="wechat-detail-form">
							<!-- 姓名 -->
							<view class="form-item">
								<view class="form-label">
									<text class="label-text">姓名</text>
								</view>
								<view class="form-input">
									<input
										class="input-field"
										type="text"
										placeholder="请输入真实姓名"
										v-model="wechatInfo.name"
									/>
								</view>
							</view>

							<!-- 微信账号 -->
							<view class="form-item">
								<view class="form-label">
									<text class="label-text">账号</text>
								</view>
								<view class="form-input">
									<input
										class="input-field"
										type="text"
										placeholder="请输入微信号/手机号"
										v-model="wechatInfo.account"
									/>
								</view>
							</view>

							<!-- 收款码 -->
							<view class="form-item">
								<view class="form-label">
									<text class="label-text">收款码</text>
								</view>
								<view class="form-input">
									<view class="upload-container">
										<view v-if="!wechatInfo.qrCode" class="upload-btn" @click="uploadQrCode">
											<view class="upload-icon">+</view>
											<text class="upload-text">上传收款码</text>
										</view>
										<view v-else class="qr-preview" @click="uploadQrCode">
											<image class="qr-image" :src="wechatInfo.qrCode" mode="aspectFit"></image>
											<view class="change-btn">
												<text class="change-text">更换</text>
											</view>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>

					<!-- 提现到银行卡 -->
					<view class="method-section">
						<view class="section-header">
							<view class="section-line"></view>
							<text class="section-title">提现到银行卡</text>
						</view>

						<!-- 银行卡列表 -->
						<view class="bank-card-list">
							<view class="bank-card-item" v-for="card in bankCards" :key="card.id" @click="selectWithdrawMethod(card.id)">
								<view class="card-info">
									<text class="bank-name">{{ card.bankName }}</text>
									<text class="card-number">(****{{ card.cardNumber }})</text>
								</view>
								<view class="radio-btn" :class="{ active: selectedMethod === card.id }">
									<view class="radio-inner"></view>
								</view>
							</view>

							<!-- 没有银行卡时的提示 -->
							<view v-if="bankCards.length === 0" class="no-bank-card">
								<text class="no-card-text">暂无银行卡，请先添加</text>
							</view>

							<view class="add-card-item" @click="addNewCard">
								<text class="add-text">添加新卡提现</text>
								<uni-icons type="right" size="16" color="#999999"></uni-icons>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { userApi } from '@/api/index.js';
import { settlementList } from '@/api/user.js';

export default {
	data() {
		return {
			availableAmount: 0, // 可提现金额
			pendingAmount: 0, // 待入账金额
			withdrawingAmount: 0, // 提现中金额
			totalWithdrawn: 0, // 已提现金额
			selectedMethod: 'wechat', // 选中的提现方式
			withdrawAmount: '', // 提现金额
			minAmount: 10, // 最低提现金额
			maxAmount: 5000, // 最高提现金额
			showMethodModal: false, // 是否显示提现方式选择弹窗
			// 提现配置
			tixianConfig: {
				minAmt: 10,
				fee: 0,
				rate: 0,
				flag: true
			},

			// 银行卡信息
			bankCards: [],
			// 微信提现信息
			wechatInfo: {
				name: '',
				account: '',
				qrCode: ''
			}
		}
	},
	computed: {
		// 是否可以提现
		canWithdraw() {
			const amount = parseFloat(this.withdrawAmount);
			const basicCheck = amount >= this.minAmount &&
				amount <= this.maxAmount &&
				amount <= this.availableAmount &&
				this.selectedMethod;

			// 如果选择微信提现，需要验证微信信息
			if (this.selectedMethod === 'wechat') {
				return basicCheck &&
					this.wechatInfo.name.trim() !== '' &&
					this.wechatInfo.account.trim() !== '' &&
					this.wechatInfo.qrCode !== '';
			}

			return basicCheck;
		},

		// 当前选中方式的显示信息
		currentMethodInfo() {
			if (this.selectedMethod === 'wechat') {
				return {
					name: '提现到微信',
					icon: '/static/images/icon_weixin_pay.png'
				};
			} else {
				// 查找对应的银行卡信息
				const bankCard = this.bankCards.find(card => card.id === this.selectedMethod);
				if (bankCard) {
					return {
						name: `${bankCard.bankName}(****${bankCard.cardNumber})`,
						icon: bankCard.icon
					};
				}
			}
			return {
				name: '提现到微信',
				icon: '/static/images/icon_weixin_pay.png'
			};
		},

		// 动态生成提现手续费提示文本
		withdrawNoticeText() {
			const { rate, fee } = this.tixianConfig;
			let feeText = '';

			if (rate > 0 && fee > 0) {
				feeText = `${rate}%+${fee}`;
			} else if (rate > 0) {
				feeText = `${rate}%`;
			} else if (fee > 0) {
				feeText = `${fee}`;
			} else {
				return '注意：您好，提现无手续费，感谢理解！';
			}

			return `注意：您好，提现会收取${feeText}手续费（银行收取），实际到账=提现金额-手续费，感谢理解！`;
		}
	},
	onLoad() {
		// 页面加载时的初始化操作
		this.loadWithdrawData();
		this.loadBankCards();
	},

	// 下拉刷新
	onPullDownRefresh() {
		Promise.all([
			this.loadWithdrawData(),
			this.loadBankCards()
		]).finally(() => {
			uni.stopPullDownRefresh();
		});
	},
	methods: {
		// 加载提现数据
		async loadWithdrawData() {
			try {
				const res = await userApi.getAccountBalance();
				if (res && res.data) {
					this.availableAmount = res.data.balance;
					this.pendingAmount = res.data.drz;
					this.withdrawingAmount = res.data.tiXianIng;
					this.totalWithdrawn = res.data.yiTiXian;

					// 更新提现配置
					if (res.data.tixianConfig) {
						this.tixianConfig = res.data.tixianConfig;
						this.minAmount = res.data.tixianConfig.minAmt;
					}
				}
			} catch (error) {
				uni.showToast({
					title: '获取余额信息失败',
					icon: 'none'
				});
			}
		},

		// 加载银行卡列表
		async loadBankCards() {
			try {
				const res = await settlementList();
				if (res.code === 0) {
					this.bankCards = (res.data || []).map(item => {
						// 解析cardInfo JSON字符串
						let cardInfo = {};
						try {
							cardInfo = JSON.parse(item.cardInfo || '{}');
						} catch (error) {
							console.error('解析cardInfo失败:', error);
							cardInfo = {};
						}

						return {
							id: item.id,
							bankName: cardInfo.bankName || '未知银行',
							cardNumber: cardInfo.accNo ? cardInfo.accNo.substring(cardInfo.accNo.length - 4) : '',
							icon: '/static/images/icon_bank_card.png',
							// 保留完整信息用于显示
							fullCardNumber: cardInfo.accNo || '',
							accName: cardInfo.accName || ''
						};
					});
				}
			} catch (error) {
				console.error('获取银行卡列表失败:', error);
			}
		},

		// 跳转到提现记录页面
		goToRecord() {
			uni.navigateTo({
				url: '/subpages/finance/withdraw-record'
			});
		},

		// 选择提现方式（打开弹窗）
		selectMethod() {
			this.showMethodModal = true;
		},

		// 关闭提现方式弹窗
		closeMethodModal() {
			this.showMethodModal = false;
		},

		// 选择具体的提现方式
		selectWithdrawMethod(method) {
			this.selectedMethod = method;
			// 如果不是微信提现，清空微信信息（可选）
			// if (method !== 'wechat') {
			//     this.wechatInfo = {
			//         name: '',
			//         account: '',
			//         qrCode: ''
			//     };
			// }
			this.showMethodModal = false;
		},

		// 添加新银行卡
		addNewCard() {
			this.showMethodModal = false;
			uni.navigateTo({
				url: '/subpages/finance/add-card'
			});
		},

		// 上传收款码
		uploadQrCode() {
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					const tempFilePath = res.tempFilePaths[0];
					// 先显示本地图片
					this.wechatInfo.qrCode = tempFilePath;
					// 上传图片到服务器
					this.uploadQrCodeToServer(tempFilePath);
				},
				fail: () => {
					uni.showToast({
						title: '选择图片失败',
						icon: 'none'
					});
				}
			});
		},

		// 上传收款码到服务器
		async uploadQrCodeToServer(filePath) {
			try {
				uni.showLoading({
					title: '上传中...'
				});

				// 调用上传文件接口
				const uploadRes = await userApi.uploadFile(filePath);
				console.log('上传收款码响应:', uploadRes);

				if (uploadRes.data && uploadRes.data.url) {
					// 更新收款码URL为服务器地址
					this.wechatInfo.qrCode = uploadRes.data.url;

					uni.hideLoading();
					uni.showToast({
						title: '收款码上传成功',
						icon: 'success'
					});
				} else {
					throw new Error('上传失败，未获取到图片地址');
				}
			} catch (error) {
				uni.hideLoading();
				console.error('上传收款码失败:', error);

				// 恢复为空，让用户重新上传
				this.wechatInfo.qrCode = '';

				uni.showToast({
					title: error.msg || '收款码上传失败',
					icon: 'none'
				});
			}
		},

		// 金额输入处理
		onAmountInput(e) {
			let value = e.detail.value;
			// 限制小数点后两位
			if (value.includes('.')) {
				const parts = value.split('.');
				if (parts[1] && parts[1].length > 2) {
					value = parts[0] + '.' + parts[1].substring(0, 2);
					this.withdrawAmount = value;
				}
			}
		},

		// 选择快捷金额
		selectQuickAmount(amount) {
			this.withdrawAmount = amount.toString();
		},

		// 选择全部金额
		selectAllAmount() {
			this.withdrawAmount = this.availableAmount.toString();
		},

		// 处理提现
		handleWithdraw() {
			if (!this.canWithdraw) {
				// 给出具体的错误提示
				if (this.selectedMethod === 'wechat') {
					if (!this.wechatInfo.name.trim()) {
						uni.showToast({
							title: '请输入真实姓名',
							icon: 'none'
						});
						return;
					}
					if (!this.wechatInfo.account.trim()) {
						uni.showToast({
							title: '请输入微信账号',
							icon: 'none'
						});
						return;
					}
					if (!this.wechatInfo.qrCode) {
						uni.showToast({
							title: '请上传收款码',
							icon: 'none'
						});
						return;
					}
				}
				return;
			}

			const amount = parseFloat(this.withdrawAmount);
			let confirmContent = '';

			if (this.selectedMethod === 'wechat') {
				confirmContent = `确认提现 ${amount} 元到微信（${this.wechatInfo.name}）吗？`;
			} else {
				const bankCard = this.bankCards.find(card => card.id === this.selectedMethod);
				if (bankCard) {
					confirmContent = `确认提现 ${amount} 元到${bankCard.bankName}(****${bankCard.cardNumber})吗？`;
				} else {
					confirmContent = `确认提现 ${amount} 元吗？`;
				}
			}

			// 显示确认弹窗
			uni.showModal({
				title: '确认提现',
				content: confirmContent,
				success: (res) => {
					if (res.confirm) {
						this.submitWithdraw(amount);
					}
				}
			});
		},

		// 提交提现请求
		submitWithdraw(amount) {
			// 显示加载中
			uni.showLoading({
				title: '提现中...'
			});

			// 模拟API调用
			setTimeout(() => {
				uni.hideLoading();

				// 模拟成功和失败的情况（这里可以根据实际API返回结果判断）
				const isSuccess = Math.random() > 0.2; // 80%成功率，用于演示

				if (isSuccess) {
					// 提现成功
					// 更新数据
					this.availableAmount -= amount;
					this.totalWithdrawn += amount;
					this.withdrawAmount = '';

					// 跳转到提现成功页面
					uni.navigateTo({
						url: `/subpages/finance/withdraw-success?amount=${amount}`
					});
				} else {
					// 提现失败
					const failReason = '账户余额不足或系统繁忙，请稍后重试';
					uni.navigateTo({
						url: `/subpages/finance/withdraw-fail?amount=${amount}&reason=${encodeURIComponent(failReason)}`
					});
				}
			}, 2000);
		}
	}
}
</script>

<style scoped>
.page {
	min-height: 100vh;
	background-color: #f5f5f5;
	padding-bottom: 120rpx;
}

/* 提现数据模块 */
.withdraw-data-section {
	position: relative;
	margin: 20rpx;
	border-radius: 20rpx;
	overflow: hidden;
}

.bg-image {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 1;
}

.data-content {
	position: relative;
	background: url('/static/images/tixianbg.jpg') no-repeat center center;
	background-size: cover;
	z-index: 2;
	display: flex;
	flex-direction: column;
	padding: 40rpx;
	box-sizing: border-box;
}

/* 主要金额显示区域 */
.main-amount-section {
	display: flex;
	flex-direction: column;
	color: #ffffff;
	position: relative;
	padding-left: 45rpx;
}

.amount-label {
	font-size: 28rpx;
	opacity: 0.9;
	margin-bottom: 20rpx;
}

.main-amount {
	font-size: 54rpx;
	font-weight: bold;
	margin-bottom: 20rpx;
}

.record-link {
	position: absolute;
	top: 70rpx;
	right: 0;
	display: flex;
	align-items: center;
}

.record-text {
	font-size: 26rpx;
	opacity: 0.9;
	margin-right: 10rpx;
}

.arrow {
	font-size: 24rpx;
	opacity: 0.9;
}

/* 底部数据区域 */
.bottom-data-section {
	display: flex;
	justify-content: space-between;
	padding-top: 30rpx;
}

.data-item {
	text-align: center;
	color: #ffffff;
	flex: 1;
}

.data-label {
	font-size: 24rpx;
	margin-bottom: 15rpx;
	opacity: 0.8;
}

.data-amount {
	font-size: 32rpx;
	font-weight: bold;
}

/* 通用标题样式 */
.section-title {
	display: flex;
	align-items: center;
}

.title-line {
	width: 8rpx;
	height: 32rpx;
	background: linear-gradient(0deg, #1fd3ff, #7577fe);
	border-radius: 4rpx;
	margin-right: 20rpx;
}

.title-text {
	font-size: 32rpx;
	font-weight: bold;
	color: #333333;
}

/* 提现方式模块 */
.withdraw-method-section {
	background-color: #ffffff;
	margin: 20rpx;
	border-radius: 20rpx;
}

.method-list {
	padding: 0 30rpx;
}

.method-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx 0;
}

.method-left {
	flex: 1;
}

.method-label {
	font-size: 32rpx;
	color: #333333;
}

.method-right {
	display: flex;
	align-items: center;
}

.wechat-icon {
	width: 40rpx;
	height: 40rpx;
	margin-right: 15rpx;
}

.method-name {
	font-size: 29rpx;
	color: #000000;
	margin-right: 15rpx;
}

.arrow {
	font-size: 28rpx;
	color: #999999;
}

/* 提现金额模块 */
.withdraw-amount-section {
	background-color: #ffffff;
	margin: 20rpx;
	border-radius: 20rpx;
	padding: 40rpx 30rpx;
}

.amount-label-section {
	margin-bottom: 40rpx;
}

.amount-label {
	font-size: 28rpx;
	color: #999999;
}

.amount-input-container {
	margin-bottom: 30rpx;
}

.input-wrapper {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
}

.currency-symbol {
	font-size: 60rpx;
	color: #333333;
	margin-right: 20rpx;
}

.amount-input {
	flex: 1;
	font-size: 35rpx;
	color: #333333;
	border: none;
	outline: none;
}

.tip-text {
	font-size: 26rpx;
	color: #999999;
}



.notice-section {
	padding: 30rpx;
	background-color: #f8f8f8;
	border-radius: 10rpx;
}

.notice-text {
	font-size: 24rpx;
	color: #999999;
	line-height: 1.6;
	padding: 0 20rpx;
}

/* 提现按钮 */
.withdraw-btn-section {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	padding: 30rpx;
	margin-bottom: 30rpx;
}

.withdraw-btn {
	width: 100%;
	height: 88rpx;
	background-color: #4D40E5;
	border-radius: 20rpx;
	border: none;
	color: #ffffff;
	font-size: 32rpx;
	/* 字体间距 */
	letter-spacing: 5rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.withdraw-btn.disabled {
	background: #cccccc;
	color: #999999;
}

.withdraw-btn::after {
	border: none;
}

/* 弹窗样式 */
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: flex-end;
}

.method-modal {
	width: 100%;
	background-color: #ffffff;
	border-radius: 20rpx 20rpx 0 0;
	max-height: 80%;
	overflow-y: auto;
}

.modal-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx 40rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333333;
}

.close-btn {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.close-icon {
	font-size: 40rpx;
	color: #999999;
}

.modal-content {
	padding: 0 40rpx 40rpx;
}

.method-section {
	margin-bottom: 40rpx;
}

.section-header {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
	margin-top: 30rpx;
}

.section-line {
	width: 6rpx;
	height: 30rpx;
	background: linear-gradient(0deg, #1fd3ff, #7577fe);
	border-radius: 3rpx;
	margin-right: 20rpx;
}

.section-title {
	font-size: 28rpx;
	color: #333333;
	font-weight: 500;
}

.method-option {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.option-content {
	display: flex;
	align-items: center;
}

.wechat-icon-modal {
	width: 40rpx;
	height: 40rpx;
	margin-right: 20rpx;
}

.option-text {
	font-size: 28rpx;
	color: #333333;
}

.check-icon {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	background-color: #4D40E5;
	display: flex;
	align-items: center;
	justify-content: center;
}

.check-mark {
	color: #ffffff;
	font-size: 24rpx;
	font-weight: bold;
}

/* 微信提现详细表单 */
.wechat-detail-form {
	margin-top: 20rpx;
	padding: 0 20rpx;
}

.form-item {
	display: flex;
	align-items: flex-start;
	margin-bottom: 20rpx;
	min-height: 60rpx;
}

/* 收款码表单项特殊处理 */
.form-item:last-child {
	align-items: flex-start;
}

.form-label {
	width: 120rpx;
	padding-top: 15rpx;
	flex-shrink: 0;
}

.label-text {
	font-size: 28rpx;
	color: #333333;
}

.form-input {
	flex: 1;
	margin-left: 20rpx;
}

.input-field {
	height: 60rpx;
	padding: 0 20rpx;
	border: 1rpx solid #e0e0e0;
	border-radius: 10rpx;
	font-size: 28rpx;
	color: #333333;
	background-color: #f8f8f8;
}

.input-field::placeholder {
	color: #999999;
}

/* 上传组件样式 */
.upload-container {
	width: 100%;
}

.upload-btn {
	width: 120rpx;
	height: 120rpx;
	border: 2rpx dashed #e0e0e0;
	border-radius: 10rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	background-color: #f8f8f8;
}

.upload-icon {
	width: 40rpx;
	height: 40rpx;
	margin-bottom: 5rpx;
	font-size: 36rpx;
	color: #999999;
	display: flex;
	align-items: center;
	justify-content: center;
}

.upload-text {
	font-size: 20rpx;
	color: #999999;
}

.qr-preview {
	position: relative;
	width: 120rpx;
	height: 120rpx;
	border-radius: 10rpx;
	overflow: hidden;
}

.qr-image {
	width: 100%;
	height: 100%;
}

.change-btn {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 30rpx;
	background-color: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
}

.change-text {
	color: #ffffff;
	font-size: 20rpx;
}



.bank-card-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.card-info {
	display: flex;
	align-items: center;
}

.bank-name {
	font-size: 28rpx;
	color: #333333;
	margin-right: 10rpx;
}

.card-number {
	font-size: 28rpx;
	color: #999999;
}

.radio-btn {
	width: 40rpx;
	height: 40rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.radio-btn.active {
	border-color: #4D40E5;
}

.radio-inner {
	width: 20rpx;
	height: 20rpx;
	border-radius: 50%;
	background-color: transparent;
}

.radio-btn.active .radio-inner {
	background-color: #4D40E5;
}

.add-card-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx 0;
}

.add-text {
	font-size: 28rpx;
	color: #333333;
}

.no-bank-card {
	padding: 30rpx 0;
	text-align: center;
}

.no-card-text {
	font-size: 28rpx;
	color: #999999;
}

.arrow {
	font-size: 24rpx;
	color: #999999;
}
</style>
