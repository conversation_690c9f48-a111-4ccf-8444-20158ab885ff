# 提现接口接入说明

## 接口信息
- **接口地址**: `POST /app/account/tixian`
- **接口描述**: 用户申请提现
- **需要登录**: 是

## 请求参数

```javascript
{
  "amt": 100.00,           // 提现金额（必填）
  "settlementId": 123,     // 银行卡ID（银行卡提现时必填，微信提现传null）
  "name": "张三",          // 姓名（微信提现时必填）
  "account": "***********", // 账号（微信提现时必填）
  "skmImg": "https://..."   // 收款码图片URL（微信提现时必填）
}
```

## 参数说明

### 通用参数
- **amt**: 提现金额，数字类型，必填

### 银行卡提现
- **settlementId**: 银行卡ID，从银行卡列表接口获取
- **name**: 传空字符串
- **account**: 传空字符串  
- **skmImg**: 传空字符串

### 微信提现
- **settlementId**: 传 null
- **name**: 真实姓名，字符串类型，必填
- **account**: 微信号或手机号，字符串类型，必填
- **skmImg**: 收款码图片URL，字符串类型，必填

## 代码实现

### API接口定义
```javascript
// 申请提现
applyWithdraw(data) {
    return post('/app/account/tixian', data);
}
```

### 调用示例

#### 微信提现
```javascript
const withdrawData = {
    amt: 100.00,
    settlementId: null,
    name: "张三",
    account: "***********", 
    skmImg: "https://example.com/qrcode.jpg"
};

const res = await userApi.applyWithdraw(withdrawData);
```

#### 银行卡提现
```javascript
const withdrawData = {
    amt: 100.00,
    settlementId: 123,
    name: "",
    account: "",
    skmImg: ""
};

const res = await userApi.applyWithdraw(withdrawData);
```

## 响应处理

### 成功响应
```javascript
{
  "code": 0,
  "msg": "success",
  "data": {...}
}
```

### 失败响应
```javascript
{
  "code": 非0值,
  "msg": "错误信息",
  "data": null
}
```

## 错误处理

1. **参数验证**: 提交前验证必填参数
2. **网络错误**: 捕获网络异常并提示用户
3. **业务错误**: 根据返回的错误信息提示用户
4. **成功处理**: 刷新余额数据，跳转成功页面

## 注意事项

1. **金额验证**: 确保提现金额在允许范围内
2. **微信信息**: 微信提现时三个参数都必须填写
3. **银行卡ID**: 银行卡提现时确保ID有效
4. **图片上传**: 收款码必须先上传获取URL
5. **余额刷新**: 提现成功后刷新账户余额
