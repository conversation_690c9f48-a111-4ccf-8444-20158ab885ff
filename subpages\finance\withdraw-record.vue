<template>
	<view class="page">
		<view class="record-container">
			<view class="record-item" v-for="(item, index) in recordList" :key="index" @click="goToDetail(item)">
				<view class="record-left">
					<image class="record-icon" :src="item.icon" mode="aspectFit"></image>
					<view class="record-info"> 
						<view class="record-title-row">
							<text class="record-title">{{ item.title }}</text>
							<view class="record-status" v-if="item.status !== 'success'">
								<image class="status-bg" :src="item.statusBg" mode="aspectFit"></image>
								<text class="status-text">{{ item.statusText }}</text>
							</view>
						</view>
						<text class="record-time">{{ item.time }}</text>
					</view>
				</view>
				<view class="record-right">
					<view class="amount-info">
						<text class="record-amount">¥{{ item.amount }}</text>
						<text class="real-amount" v-if="item.fee > 0">实际到账¥{{ item.realAmount }}</text>
					</view>
					<uni-icons type="right" size="16" color="#999999"></uni-icons>
				</view>
			</view>
		</view>

		<view class="empty-state" v-if="recordList.length === 0">
			<text class="empty-text">暂无提现记录</text>
		</view>
	</view>
</template>

<script>
	import { userApi } from '@/api/index.js';

	export default {
		data() {
			return {
				recordList: []
			}
		},
		onLoad() {
			this.loadRecordData();
		},

		// 下拉刷新
		onPullDownRefresh() {
			this.loadRecordData().finally(() => {
				uni.stopPullDownRefresh();
			});
		},
		methods: {
			// 加载提现记录数据
			async loadRecordData() {
				try {
					const res = await userApi.getWithdrawRecords();

					if (res && res.code === 0 && res.data) {
						this.recordList = this.formatRecordData(res.data);
					}
				} catch (error) {
					console.error('获取提现记录失败:', error);
					uni.showToast({
						title: '获取提现记录失败',
						icon: 'none'
					});
				}
			},

			// 格式化提现记录数据
			formatRecordData(data) {
				return data.map(item => {
					// 解析银行卡信息
					let cardInfo = {};
					try {
						cardInfo = JSON.parse(item.cardInfo || '{}');
					} catch (error) {
						console.error('解析cardInfo失败:', error);
						cardInfo = {};
					}

					// 根据type判断提现方式
					// type: "1" = 银行卡提现, "2" = 微信提现
					const isWechat = item.type === '2';

					// 格式化状态
					const statusInfo = this.getStatusInfo(item.withdrawalStatus);

					// 生成标题
					const title = isWechat ? '佣金提现至微信' : `佣金提现至${cardInfo.bankName || '银行卡'}`;

					return {
						id: item.id,
						orderNo: item.orderNo,
						title: title,
						amount: item.withdrawalAmt,
						realAmount: item.withdrawalRealAmt, // 实际到账金额
						fee: item.withdrawalFee, // 手续费
						time: item.createDate,
						status: statusInfo.status,
						statusText: statusInfo.statusText,
						statusBg: statusInfo.statusBg,
						icon: isWechat ? '/static/images/icon_weixin_kind.png' : '/static/images/icon_car_kind.png',
						cardInfo: cardInfo,
						isWechat: isWechat,
						withdrawalStatus: item.withdrawalStatus,
						withdrawalResult: item.withdrawalResult
					};
				});
			},

			// 获取状态信息
			getStatusInfo(withdrawalStatus) {
				switch (withdrawalStatus) {
					case '0':
						return {
							status: 'pending',
							statusText: '待处理',
							statusBg: '/static/images/blue.png'
						};
					case '1':
						return {
							status: 'processing',
							statusText: '提现中',
							statusBg: '/static/images/blue.png'
						};
					case '2':
						return {
							status: 'success',
							statusText: '',
							statusBg: ''
						};
					case '3':
						return {
							status: 'failed',
							statusText: '提现失败',
							statusBg: '/static/images/red.png'
						};
					default:
						return {
							status: 'unknown',
							statusText: '未知状态',
							statusBg: '/static/images/blue.png'
						};
				}
			},
			goToDetail(item) {
				// 跳转到提现明细页面，传递更多详细信息
				const params = {
					id: item.id,
					orderNo: item.orderNo,
					status: item.withdrawalStatus,
					amount: item.amount,
					realAmount: item.realAmount,
					fee: item.fee,
					submitTime: encodeURIComponent(item.time),
					isWechat: item.isWechat,
					withdrawalResult: item.withdrawalResult || ''
				};

				const queryString = Object.keys(params)
					.map(key => `${key}=${params[key]}`)
					.join('&');

				uni.navigateTo({
					url: `/subpages/finance/withdraw-detail?${queryString}`
				});
			}
		}
	}
</script>

<style scoped>
	.page {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding: 24rpx;
	}

	.record-container {
		background-color: #ffffff;
		border-radius: 24rpx;
		overflow: hidden;
	}

	.record-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 32rpx 24rpx;
		border-bottom: 1rpx solid #f5f5f5;
	}

	.record-item:last-child {
		border-bottom: none;
	}

	.record-left {
		display: flex;
		align-items: center;
		flex: 1;
	}

	.record-icon {
		width: 48rpx;
		height: 48rpx;
		margin-right: 24rpx;
	}

	.record-info {
		flex: 1;
	}

	.record-title-row {
		display: flex;
		align-items: center;
		margin-bottom: 8rpx;
	}

	.record-title {
		font-size: 32rpx;
		color: #333333;
		font-weight: 500;
		margin-right: 16rpx;
	}

	.record-status {
		position: relative;
		display: inline-flex;
		align-items: center;
		justify-content: center;
		height: 32rpx;
		padding: 0 16rpx;
		border-radius: 16rpx;
	}

	.status-bg {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		border-radius: 16rpx;
	}

	.status-text {
		position: relative;
		z-index: 1;
		font-size: 22rpx;
		color: #ffffff;
		font-weight: 500;
	}

	.record-time {
		font-size: 26rpx;
		color: #999999;
	}

	.record-right {
		display: flex;
		align-items: center;
	}

	.amount-info {
		display: flex;
		flex-direction: column;
		align-items: flex-end;
		margin-right: 16rpx;
	}

	.record-amount {
		font-size: 32rpx;
		color: #333333;
		font-weight: bold;
	}

	.real-amount {
		font-size: 24rpx;
		color: #999999;
		margin-top: 4rpx;
	}

	.arrow-icon {
		width: 24rpx;
		height: 24rpx;
	}

	.empty-state {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 400rpx;
	}

	.empty-text {
		font-size: 28rpx;
		color: #999999;
	}
</style>
